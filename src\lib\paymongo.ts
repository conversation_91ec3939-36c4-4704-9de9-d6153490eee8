/**
 * PayMongo API Client for GCash Payment Integration
 * This module provides functions to interact with PayMongo's API for processing GCash payments
 */

const PAYMONGO_BASE_URL = 'https://api.paymongo.com/v1';

export interface PayMongoPaymentIntent {
  id: string;
  type: string;
  attributes: {
    amount: number;
    currency: string;
    description: string;
    statement_descriptor: string;
    status: string;
    livemode: boolean;
    client_key: string;
    created_at: number;
    updated_at: number;
    last_payment_error?: any;
    payment_method_allowed: string[];
    payments: any[];
    next_action?: {
      type: string;
      redirect?: {
        url: string;
        return_url: string;
      };
    };
    payment_method_options?: {
      card?: {
        request_three_d_secure: string;
      };
    };
    capture_type: string;
    setup_future_usage?: string;
  };
}

export interface PayMongoSource {
  id: string;
  type: string;
  attributes: {
    amount: number;
    billing?: any;
    currency: string;
    description: string;
    livemode: boolean;
    redirect: {
      checkout_url: string;
      failed: string;
      success: string;
    };
    status: string;
    type: string;
    created_at: number;
    updated_at: number;
  };
}

export interface CreatePaymentIntentData {
  amount: number; // Amount in centavos (e.g., 10000 = PHP 100.00)
  currency: string;
  description: string;
  payment_method_allowed: string[];
  capture_type?: string;
  statement_descriptor?: string;
}

export interface CreateSourceData {
  amount: number; // Amount in centavos
  currency: string;
  type: string; // 'gcash' for GCash payments
  redirect: {
    success: string;
    failed: string;
  };
  billing?: {
    name: string;
    email: string;
    phone?: string;
  };
  description?: string;
}

/**
 * Create a payment intent for GCash payment
 */
export async function createPaymentIntent(data: CreatePaymentIntentData): Promise<PayMongoPaymentIntent> {
  const secretKey = process.env.PAYMONGO_SECRET_KEY;
  
  if (!secretKey) {
    throw new Error('PayMongo secret key is not configured');
  }

  const response = await fetch(`${PAYMONGO_BASE_URL}/payment_intents`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${Buffer.from(secretKey + ':').toString('base64')}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      data: {
        attributes: {
          amount: data.amount,
          currency: data.currency || 'PHP',
          description: data.description,
          payment_method_allowed: data.payment_method_allowed || ['gcash'],
          capture_type: data.capture_type || 'automatic',
          statement_descriptor: data.statement_descriptor || 'Rainbow Paws',
        }
      }
    })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`PayMongo API Error: ${error.errors?.[0]?.detail || 'Unknown error'}`);
  }

  const result = await response.json();
  return result.data;
}

/**
 * Create a source for GCash payment (alternative method)
 */
export async function createSource(data: CreateSourceData): Promise<PayMongoSource> {
  const secretKey = process.env.PAYMONGO_SECRET_KEY;
  
  if (!secretKey) {
    throw new Error('PayMongo secret key is not configured');
  }

  const response = await fetch(`${PAYMONGO_BASE_URL}/sources`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${Buffer.from(secretKey + ':').toString('base64')}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      data: {
        attributes: {
          amount: data.amount,
          currency: data.currency || 'PHP',
          type: data.type,
          redirect: data.redirect,
          billing: data.billing,
          description: data.description,
        }
      }
    })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`PayMongo API Error: ${error.errors?.[0]?.detail || 'Unknown error'}`);
  }

  const result = await response.json();
  return result.data;
}

/**
 * Retrieve a payment intent by ID
 */
export async function retrievePaymentIntent(paymentIntentId: string): Promise<PayMongoPaymentIntent> {
  const secretKey = process.env.PAYMONGO_SECRET_KEY;
  
  if (!secretKey) {
    throw new Error('PayMongo secret key is not configured');
  }

  const response = await fetch(`${PAYMONGO_BASE_URL}/payment_intents/${paymentIntentId}`, {
    method: 'GET',
    headers: {
      'Authorization': `Basic ${Buffer.from(secretKey + ':').toString('base64')}`,
      'Content-Type': 'application/json',
    }
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`PayMongo API Error: ${error.errors?.[0]?.detail || 'Unknown error'}`);
  }

  const result = await response.json();
  return result.data;
}

/**
 * Retrieve a source by ID
 */
export async function retrieveSource(sourceId: string): Promise<PayMongoSource> {
  const secretKey = process.env.PAYMONGO_SECRET_KEY;
  
  if (!secretKey) {
    throw new Error('PayMongo secret key is not configured');
  }

  const response = await fetch(`${PAYMONGO_BASE_URL}/sources/${sourceId}`, {
    method: 'GET',
    headers: {
      'Authorization': `Basic ${Buffer.from(secretKey + ':').toString('base64')}`,
      'Content-Type': 'application/json',
    }
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`PayMongo API Error: ${error.errors?.[0]?.detail || 'Unknown error'}`);
  }

  const result = await response.json();
  return result.data;
}

/**
 * Convert PHP amount to centavos (PayMongo uses centavos)
 */
export function phpToCentavos(amount: number): number {
  return Math.round(amount * 100);
}

/**
 * Convert centavos to PHP amount
 */
export function centavosToPHP(centavos: number): number {
  return centavos / 100;
}

/**
 * Validate webhook signature (for webhook security)
 */
export function validateWebhookSignature(payload: string, signature: string): boolean {
  // PayMongo webhook signature validation would go here
  // For now, we'll implement basic validation
  return true; // TODO: Implement proper signature validation
}
